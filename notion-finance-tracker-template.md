# 💰 Personal & Business Finance Tracker

> **Welcome to your comprehensive financial management system!** This template helps you track income, expenses, budgets, investments, debts, and financial goals with powerful automation and insights.

---

## 📊 Financial Dashboard

### 🎯 Quick Stats
- **Monthly Income:** $5,250.00
- **Monthly Expenses:** $3,890.50
- **Net Income:** $1,359.50
- **Savings Rate:** 25.9%
- **Budget Adherence:** 87%

### 💡 Financial Health Score: 8.2/10

---

## 🗂️ Core Databases

### 💳 Transactions Database

| Date | Description | Category | Account | Type | Amount | Tags |
|------|-------------|----------|---------|------|--------|------|
| 2024-01-15 | Salary Payment | Income | Checking | Income | $4,500.00 | salary, primary |
| 2024-01-14 | Grocery Shopping | Food | Credit Card | Expense | -$125.50 | groceries, weekly |
| 2024-01-13 | Freelance Project | Income | PayPal | Income | $750.00 | freelance, side-hustle |
| 2024-01-12 | Electric Bill | Utilities | Checking | Expense | -$89.30 | utilities, monthly |
| 2024-01-11 | Coffee Shop | Food | Debit Card | Expense | -$12.50 | coffee, dining |
| 2024-01-10 | Investment Deposit | Investment | Brokerage | Transfer | -$500.00 | investment, monthly |
| 2024-01-09 | Gas Station | Transportation | Credit Card | Expense | -$45.20 | gas, car |
| 2024-01-08 | Online Course | Education | Credit Card | Expense | -$199.00 | education, skill-building |

**Properties:**
- Date: Date
- Description: Title
- Category: Relation (Categories)
- Account: Relation (Accounts)
- Type: Select (Income, Expense, Transfer)
- Amount: Number (Currency)
- Tags: Multi-select
- Month: Formula (formatDate(prop("Date"), "MMMM YYYY"))
- Year: Formula (formatDate(prop("Date"), "YYYY"))

---

### 🏷️ Categories Database

| Category | Type | Budget | Spent This Month | Remaining | Status |
|----------|------|--------|------------------|-----------|--------|
| Food | Expense | $600.00 | $438.50 | $161.50 | ✅ On Track |
| Transportation | Expense | $300.00 | $245.20 | $54.80 | ✅ On Track |
| Utilities | Expense | $200.00 | $189.30 | $10.70 | ⚠️ Near Limit |
| Entertainment | Expense | $150.00 | $89.50 | $60.50 | ✅ On Track |
| Education | Expense | $250.00 | $199.00 | $51.00 | ✅ On Track |
| Salary | Income | $4,500.00 | $4,500.00 | - | ✅ Met |
| Freelance | Income | $500.00 | $750.00 | +$250.00 | 🎉 Exceeded |

**Properties:**
- Category: Title
- Type: Select (Income, Expense)
- Budget: Number (Currency)
- Spent This Month: Rollup (Sum of related transactions)
- Remaining: Formula (prop("Budget") - prop("Spent This Month"))
- Status: Formula (Conditional based on remaining amount)
- Color: Select (for visual organization)

---

### 🏦 Accounts Database

| Account Name | Type | Current Balance | Last Updated | Institution |
|--------------|------|-----------------|--------------|-------------|
| Primary Checking | Checking | $2,450.75 | 2024-01-15 | Chase Bank |
| High-Yield Savings | Savings | $15,230.50 | 2024-01-15 | Marcus |
| Emergency Fund | Savings | $8,500.00 | 2024-01-10 | Ally Bank |
| Credit Card | Credit | -$1,245.30 | 2024-01-14 | Capital One |
| Investment Account | Investment | $25,750.80 | 2024-01-12 | Fidelity |
| PayPal | Digital | $325.50 | 2024-01-13 | PayPal |

**Properties:**
- Account Name: Title
- Type: Select (Checking, Savings, Credit, Investment, Digital)
- Current Balance: Number (Currency)
- Last Updated: Date
- Institution: Text
- Active: Checkbox
- Notes: Text

---

### 📈 Budget Planning Database

| Month | Category | Budgeted | Actual | Variance | Variance % |
|-------|----------|----------|--------|----------|------------|
| January 2024 | Food | $600.00 | $438.50 | $161.50 | 26.9% |
| January 2024 | Transportation | $300.00 | $245.20 | $54.80 | 18.3% |
| January 2024 | Utilities | $200.00 | $189.30 | $10.70 | 5.4% |
| January 2024 | Entertainment | $150.00 | $89.50 | $60.50 | 40.3% |
| December 2023 | Food | $600.00 | $625.80 | -$25.80 | -4.3% |
| December 2023 | Transportation | $300.00 | $310.45 | -$10.45 | -3.5% |

**Properties:**
- Month: Title
- Category: Relation (Categories)
- Budgeted: Number (Currency)
- Actual: Rollup (Sum from Transactions)
- Variance: Formula (prop("Budgeted") - prop("Actual"))
- Variance %: Formula (prop("Variance") / prop("Budgeted") * 100)
- Status: Formula (Based on variance)

---

### 🎯 Savings Goals Database

| Goal Name | Target Amount | Current Amount | Progress | Target Date | Status |
|-----------|---------------|----------------|----------|-------------|--------|
| Emergency Fund | $10,000.00 | $8,500.00 | 85% | 2024-06-30 | 🟡 In Progress |
| Vacation Fund | $3,000.00 | $1,250.00 | 42% | 2024-08-15 | 🟡 In Progress |
| New Car Down Payment | $5,000.00 | $2,100.00 | 42% | 2024-12-31 | 🟡 In Progress |
| Home Renovation | $15,000.00 | $3,750.00 | 25% | 2025-05-01 | 🟡 In Progress |
| Retirement Boost | $25,000.00 | $25,750.00 | 103% | 2024-12-31 | ✅ Completed |

**Properties:**
- Goal Name: Title
- Target Amount: Number (Currency)
- Current Amount: Number (Currency)
- Progress: Formula (prop("Current Amount") / prop("Target Amount") * 100)
- Target Date: Date
- Status: Formula (Based on progress and date)
- Monthly Contribution: Number (Currency)
- Notes: Text

---

### 💳 Debt Tracking Database

| Debt Name | Original Amount | Current Balance | Interest Rate | Min Payment | Target Payoff |
|-----------|-----------------|-----------------|---------------|-------------|---------------|
| Credit Card 1 | $3,500.00 | $1,245.30 | 18.99% | $50.00 | 2024-08-15 |
| Student Loan | $25,000.00 | $18,750.50 | 4.5% | $285.00 | 2027-12-31 |
| Car Loan | $20,000.00 | $12,450.75 | 3.2% | $350.00 | 2026-06-30 |

**Properties:**
- Debt Name: Title
- Original Amount: Number (Currency)
- Current Balance: Number (Currency)
- Interest Rate: Number (Percent)
- Min Payment: Number (Currency)
- Target Payoff: Date
- Months Remaining: Formula
- Total Interest: Formula
- Status: Select (Active, Paid Off, Deferred)

---

### 📊 Investment Portfolio Database

| Investment | Type | Shares/Units | Purchase Price | Current Price | Current Value | Gain/Loss |
|------------|------|--------------|----------------|---------------|---------------|-----------|
| VTSAX | Mutual Fund | 125.50 | $95.20 | $108.75 | $13,649.38 | $1,700.63 |
| AAPL | Stock | 15 | $150.25 | $185.50 | $2,782.50 | $528.75 |
| BTC | Cryptocurrency | 0.25 | $35,000.00 | $42,500.00 | $10,625.00 | $1,875.00 |
| REIT Fund | ETF | 50 | $85.30 | $92.15 | $4,607.50 | $342.50 |

**Properties:**
- Investment: Title
- Type: Select (Stock, Bond, Mutual Fund, ETF, Cryptocurrency, Real Estate)
- Shares/Units: Number
- Purchase Price: Number (Currency)
- Current Price: Number (Currency)
- Current Value: Formula (prop("Shares/Units") * prop("Current Price"))
- Gain/Loss: Formula (prop("Current Value") - (prop("Shares/Units") * prop("Purchase Price")))
- Gain/Loss %: Formula
- Purchase Date: Date
- Sector: Select

---

### 🎯 Financial Goals Database

| Goal | Category | Target Date | Target Amount | Current Progress | Priority | Status |
|------|----------|-------------|---------------|------------------|----------|--------|
| Build 6-Month Emergency Fund | Security | 2024-06-30 | $15,000.00 | 57% | High | 🟡 In Progress |
| Max Out 401k | Retirement | 2024-12-31 | $23,000.00 | 35% | High | 🟡 In Progress |
| Pay Off Credit Cards | Debt Freedom | 2024-08-15 | $1,245.30 | 65% | High | 🟡 In Progress |
| Save for House Down Payment | Real Estate | 2026-01-01 | $50,000.00 | 12% | Medium | 🟡 In Progress |
| Start Side Business | Income | 2024-09-01 | $2,000.00 | 25% | Medium | 🟡 In Progress |

**Properties:**
- Goal: Title
- Category: Select (Security, Retirement, Debt Freedom, Real Estate, Income, Education)
- Target Date: Date
- Target Amount: Number (Currency)
- Current Progress: Number (Percent)
- Priority: Select (High, Medium, Low)
- Status: Select (Not Started, In Progress, Completed, On Hold)
- Notes: Text
- Related Accounts: Relation (Accounts)

---

## 📈 Monthly Financial Summary

### January 2024 Overview

**Income Breakdown:**
- Salary: $4,500.00 (85.7%)
- Freelance: $750.00 (14.3%)
- **Total Income:** $5,250.00

**Expense Breakdown:**
- Food: $438.50 (11.3%)
- Transportation: $245.20 (6.3%)
- Utilities: $189.30 (4.9%)
- Entertainment: $89.50 (2.3%)
- Education: $199.00 (5.1%)
- Investment: $500.00 (12.9%)
- Other: $228.00 (5.9%)
- **Total Expenses:** $3,890.50

**Key Metrics:**
- Net Income: $1,359.50
- Savings Rate: 25.9%
- Budget Adherence: 87%

---

## 🔄 Automated Calculations & Formulas

### Key Formulas Used:

**Monthly Net Income:**
```
prop("Total Income") - prop("Total Expenses")
```

**Savings Rate:**
```
(prop("Net Income") / prop("Total Income")) * 100
```

**Budget Variance:**
```
prop("Budgeted Amount") - prop("Actual Spent")
```

**Investment Gain/Loss:**
```
(prop("Current Value") - prop("Purchase Value")) / prop("Purchase Value") * 100
```

**Debt Payoff Timeline:**
```
prop("Current Balance") / prop("Monthly Payment")
```

**Goal Progress:**
```
(prop("Current Amount") / prop("Target Amount")) * 100
```

---

## 📱 Quick Actions & Templates

### 🔄 Monthly Review Checklist
- [ ] Update all account balances
- [ ] Review and categorize transactions
- [ ] Check budget vs actual spending
- [ ] Update investment values
- [ ] Review debt payments
- [ ] Assess goal progress
- [ ] Plan next month's budget

### 💡 Financial Health Tips
> **Tip 1:** Aim for a savings rate of at least 20% of your income
> **Tip 2:** Keep your emergency fund at 3-6 months of expenses
> **Tip 3:** Pay off high-interest debt before investing
> **Tip 4:** Review and rebalance your investment portfolio quarterly

---

## 🎨 Visual Indicators & Status

**Budget Status Colors:**
- 🟢 Green: Under budget (>10% remaining)
- 🟡 Yellow: Near budget (0-10% remaining)
- 🔴 Red: Over budget

**Goal Progress:**
- 🔴 0-25%: Just Started
- 🟡 26-75%: In Progress
- 🟢 76-99%: Almost There
- ✅ 100%+: Completed

**Account Health:**
- ✅ Healthy: Positive balance, regular activity
- ⚠️ Warning: Low balance or unusual activity
- 🚨 Alert: Negative balance or overdue payments

---

## 🛠️ Advanced Features & Setup Instructions

### 🔗 Database Relations Setup

**1. Transactions ↔ Categories**
- In Transactions: Add "Category" as Relation to Categories database
- In Categories: Add "Related Transactions" as Relation to Transactions

**2. Transactions ↔ Accounts**
- In Transactions: Add "Account" as Relation to Accounts database
- In Accounts: Add "Related Transactions" as Relation to Transactions

**3. Budget ↔ Categories**
- In Budget: Add "Category" as Relation to Categories database
- In Categories: Add "Budget Items" as Relation to Budget database

### 📊 Advanced Rollup Formulas

**Category Monthly Spending (in Categories database):**
```
Rollup of Related Transactions → Amount
Filter: formatDate(prop("Date"), "MMMM YYYY") == "January 2024" and prop("Type") == "Expense"
Calculate: Sum
```

**Account Balance Calculation (in Accounts database):**
```
Rollup of Related Transactions → Amount
Calculate: Sum
```

**Investment Portfolio Value (in Investment database):**
```
Formula: prop("Shares") * prop("Current Price")
```

### 🎯 Advanced Views & Filters

**Monthly Expense View (Transactions database):**
- Filter: Type = "Expense" AND Date = "This Month"
- Group by: Category
- Sort: Amount (Descending)

**Budget vs Actual View (Categories database):**
- Filter: Type = "Expense"
- Show: Category, Budget, Spent This Month, Remaining
- Sort: Remaining (Ascending)

**Investment Performance View (Investment database):**
- Show: Investment, Current Value, Gain/Loss, Gain/Loss %
- Sort: Gain/Loss % (Descending)

---

## 📈 Yearly Financial Analysis

### 2024 Annual Goals Dashboard

| Quarter | Income Goal | Actual Income | Expense Budget | Actual Expenses | Savings Goal | Actual Savings |
|---------|-------------|---------------|----------------|-----------------|--------------|----------------|
| Q1 2024 | $15,750.00 | $5,250.00 | $11,700.00 | $3,890.50 | $4,050.00 | $1,359.50 |
| Q2 2024 | $15,750.00 | - | $11,700.00 | - | $4,050.00 | - |
| Q3 2024 | $15,750.00 | - | $11,700.00 | - | $4,050.00 | - |
| Q4 2024 | $15,750.00 | - | $11,700.00 | - | $4,050.00 | - |
| **Total** | **$63,000.00** | **$5,250.00** | **$46,800.00** | **$3,890.50** | **$16,200.00** | **$1,359.50** |

### 📊 Category Trends (Last 12 Months)

| Category | Jan | Feb | Mar | Apr | May | Jun | Jul | Aug | Sep | Oct | Nov | Dec | Avg |
|----------|-----|-----|-----|-----|-----|-----|-----|-----|-----|-----|-----|-----|-----|
| Food | $438 | $465 | $520 | $485 | $510 | $495 | $525 | $540 | $475 | $490 | $515 | $580 | $503 |
| Transportation | $245 | $280 | $310 | $265 | $290 | $275 | $320 | $295 | $250 | $285 | $305 | $340 | $288 |
| Utilities | $189 | $195 | $175 | $165 | $145 | $135 | $155 | $165 | $175 | $185 | $205 | $225 | $176 |

---

## 💼 Business Finance Tracking

### 📋 Business Expense Categories

| Category | Tax Deductible | Q1 Budget | Q1 Actual | Remaining | Notes |
|----------|----------------|-----------|-----------|-----------|-------|
| Office Supplies | ✅ Yes | $300.00 | $125.50 | $174.50 | Printer, paper, pens |
| Software Subscriptions | ✅ Yes | $500.00 | $485.00 | $15.00 | Adobe, Notion, etc. |
| Professional Development | ✅ Yes | $800.00 | $199.00 | $601.00 | Courses, conferences |
| Marketing | ✅ Yes | $600.00 | $350.00 | $250.00 | Ads, promotional materials |
| Travel | ✅ Partial | $1,200.00 | $0.00 | $1,200.00 | Business trips |
| Meals & Entertainment | ✅ Partial | $400.00 | $89.50 | $310.50 | Client meetings |

### 💰 Revenue Streams Tracking

| Revenue Source | Type | Q1 Target | Q1 Actual | Variance | Growth Rate |
|----------------|------|-----------|-----------|----------|-------------|
| Primary Salary | W2 | $13,500.00 | $13,500.00 | $0.00 | 0% |
| Freelance Projects | 1099 | $1,500.00 | $2,250.00 | +$750.00 | +50% |
| Consulting | 1099 | $2,000.00 | $1,500.00 | -$500.00 | -25% |
| Passive Income | Investment | $300.00 | $425.00 | +$125.00 | +42% |
| Side Business | Business | $500.00 | $0.00 | -$500.00 | -100% |

---

## 🔄 Automation & Integrations

### 📱 Mobile Quick Entry Template

**Quick Expense Entry:**
```
🛒 [Amount] - [Description] - [Category]
Example: 🛒 $25.50 - Lunch at cafe - Food
```

**Quick Income Entry:**
```
💰 [Amount] - [Description] - [Source]
Example: 💰 $500.00 - Freelance payment - Freelance
```

### 🔔 Monthly Review Reminders

**Week 1:** Update account balances
**Week 2:** Categorize all transactions
**Week 3:** Review budget performance
**Week 4:** Plan next month's budget

### 📊 Key Performance Indicators (KPIs)

**Financial Health Score Calculation:**
- Emergency Fund Coverage: 25% (3+ months = 25 points)
- Debt-to-Income Ratio: 20% (<30% = 20 points)
- Savings Rate: 20% (>20% = 20 points)
- Budget Adherence: 15% (>80% = 15 points)
- Investment Diversification: 10% (3+ types = 10 points)
- Credit Utilization: 10% (<30% = 10 points)

**Current Score: 8.2/10**
- Emergency Fund: ✅ 22/25 (5.7 months coverage)
- Debt-to-Income: ✅ 18/20 (15% ratio)
- Savings Rate: ✅ 20/20 (25.9% rate)
- Budget Adherence: ✅ 13/15 (87% adherence)
- Investment Diversification: ✅ 10/10 (4 types)
- Credit Utilization: ✅ 9/10 (35% utilization)

---

## 🎨 Customization Options

### 🌈 Color Coding System

**Income Categories:**
- 🟢 Primary Income (Salary, Business)
- 🔵 Secondary Income (Freelance, Side Hustle)
- 🟡 Passive Income (Investments, Royalties)

**Expense Categories:**
- 🔴 Fixed Expenses (Rent, Insurance, Loans)
- 🟠 Variable Expenses (Food, Entertainment, Shopping)
- 🟣 Discretionary (Hobbies, Luxury items)

**Account Types:**
- 🏦 Banking (Checking, Savings)
- 💳 Credit (Credit Cards, Lines of Credit)
- 📈 Investment (Brokerage, Retirement)
- 💰 Cash & Digital (PayPal, Venmo, Cash)

### 📋 Custom Templates

**Monthly Budget Template:**
```
| Category | Last Month | This Month | Difference | Notes |
|----------|------------|------------|------------|-------|
| Housing | $1,200 | $1,200 | $0 | Rent unchanged |
| Food | $500 | $550 | +$50 | Inflation adjustment |
| Transportation | $300 | $280 | -$20 | Less driving |
```

**Investment Review Template:**
```
| Asset Class | Target % | Current % | Rebalance Needed |
|-------------|----------|-----------|------------------|
| US Stocks | 60% | 65% | Sell $500 |
| International | 20% | 15% | Buy $300 |
| Bonds | 15% | 15% | Hold |
| Real Estate | 5% | 5% | Hold |
```

---

## 🚀 Getting Started Checklist

### Initial Setup (Week 1)
- [ ] Create all 8 core databases
- [ ] Set up database relations and rollups
- [ ] Import your last 3 months of transactions
- [ ] Set up your accounts and current balances
- [ ] Define your expense categories
- [ ] Set initial budget amounts

### Customization (Week 2)
- [ ] Customize categories for your lifestyle
- [ ] Set up your savings goals
- [ ] Input your current debts
- [ ] Add your investment accounts
- [ ] Define your financial goals
- [ ] Set up automated views and filters

### Optimization (Week 3)
- [ ] Create custom formulas for your needs
- [ ] Set up monthly review templates
- [ ] Create dashboard views
- [ ] Test all calculations and rollups
- [ ] Set up mobile quick-entry methods
- [ ] Create backup of your template

### Maintenance (Ongoing)
- [ ] Weekly: Add new transactions
- [ ] Bi-weekly: Update account balances
- [ ] Monthly: Review budget vs actual
- [ ] Quarterly: Review and adjust goals
- [ ] Annually: Complete financial health assessment

---

## 🔧 Troubleshooting & Tips

### Common Issues & Solutions

**Rollups not calculating correctly:**
- Check that relations are properly set up
- Verify filter conditions in rollup formulas
- Ensure date formats match across databases

**Budget tracking inaccurate:**
- Confirm all transactions are properly categorized
- Check that expense types are correctly marked
- Verify monthly date filters are working

**Investment calculations off:**
- Update current prices regularly
- Check that share quantities are accurate
- Verify purchase price data is correct

### Pro Tips

1. **Use templates for recurring transactions** to speed up data entry
2. **Set up views for different time periods** (weekly, monthly, quarterly)
3. **Create filtered views for tax preparation** (business expenses, deductible items)
4. **Use tags for additional categorization** beyond main categories
5. **Set up conditional formatting** for visual alerts on budget overruns
6. **Create linked databases** for detailed subcategory tracking
7. **Use formulas for automatic status updates** based on thresholds
8. **Set up recurring reminders** for regular financial tasks

---

*Last Updated: January 15, 2024*
*Template Version: 2.1 - Advanced Features*

---

## 📄 Implementation Notes

This template is designed to be copied directly into Notion. Each section can be implemented as follows:

1. **Databases:** Create each database with the specified properties
2. **Relations:** Set up connections between databases as outlined
3. **Formulas:** Copy the provided formulas into the appropriate formula fields
4. **Views:** Create filtered and sorted views as described
5. **Dashboard:** Use the summary sections as callout blocks or embed database views

The template includes realistic dummy data to demonstrate functionality and can be easily customized for personal or business use.
