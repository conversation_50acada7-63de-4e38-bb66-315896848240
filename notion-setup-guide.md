# 🚀 Notion Finance Tracker - Quick Setup Guide

## Step-by-Step Implementation

### 1. Create Core Databases (30 minutes)

**Database 1: Transactions**
```
Properties to add:
- Date (Date)
- Description (Title) 
- Category (Relation → Categories)
- Account (Relation → Accounts)
- Type (Select: Income, Expense, Transfer)
- Amount (Number, Currency format)
- Tags (Multi-select)
- Month (Formula: formatDate(prop("Date"), "MMMM YYYY"))
- Year (Formula: formatDate(prop("Date"), "YYYY"))
```

**Database 2: Categories**
```
Properties to add:
- Category (Title)
- Type (Select: Income, Expense)
- Budget (Number, Currency)
- Spent This Month (Rollup from Transactions)
- Remaining (Formula: prop("Budget") - prop("Spent This Month"))
- Status (Formula: Conditional based on remaining)
- Color (Select: Red, Yellow, Green, Blue, Purple)
```

**Database 3: Accounts**
```
Properties to add:
- Account Name (Title)
- Type (Select: Checking, Savings, Credit, Investment, Digital)
- Current Balance (Number, Currency)
- Last Updated (Date)
- Institution (Text)
- Active (Checkbox)
- Notes (Text)
```

**Database 4: Budget Planning**
```
Properties to add:
- Month (Title)
- Category (Relation → Categories)
- Budgeted (Number, Currency)
- Actual (Rollup from Transactions)
- Variance (Formula: prop("Budgeted") - prop("Actual"))
- Variance % (Formula: prop("Variance") / prop("Budgeted") * 100)
```

**Database 5: Savings Goals**
```
Properties to add:
- Goal Name (Title)
- Target Amount (Number, Currency)
- Current Amount (Number, Currency)
- Progress (Formula: prop("Current Amount") / prop("Target Amount") * 100)
- Target Date (Date)
- Status (Formula based on progress)
- Monthly Contribution (Number, Currency)
```

**Database 6: Debt Tracking**
```
Properties to add:
- Debt Name (Title)
- Original Amount (Number, Currency)
- Current Balance (Number, Currency)
- Interest Rate (Number, Percent)
- Min Payment (Number, Currency)
- Target Payoff (Date)
- Status (Select: Active, Paid Off, Deferred)
```

**Database 7: Investment Portfolio**
```
Properties to add:
- Investment (Title)
- Type (Select: Stock, Bond, Mutual Fund, ETF, Cryptocurrency, Real Estate)
- Shares/Units (Number)
- Purchase Price (Number, Currency)
- Current Price (Number, Currency)
- Current Value (Formula: prop("Shares/Units") * prop("Current Price"))
- Gain/Loss (Formula: prop("Current Value") - (prop("Shares/Units") * prop("Purchase Price")))
- Purchase Date (Date)
```

**Database 8: Financial Goals**
```
Properties to add:
- Goal (Title)
- Category (Select: Security, Retirement, Debt Freedom, Real Estate, Income)
- Target Date (Date)
- Target Amount (Number, Currency)
- Current Progress (Number, Percent)
- Priority (Select: High, Medium, Low)
- Status (Select: Not Started, In Progress, Completed, On Hold)
```

### 2. Set Up Database Relations (15 minutes)

1. **Transactions ↔ Categories:**
   - In Transactions: Add "Category" relation to Categories database
   - In Categories: Relation will auto-appear as "Related Transactions"

2. **Transactions ↔ Accounts:**
   - In Transactions: Add "Account" relation to Accounts database
   - In Accounts: Relation will auto-appear as "Related Transactions"

3. **Budget ↔ Categories:**
   - In Budget: Add "Category" relation to Categories database

### 3. Configure Rollups (10 minutes)

**In Categories database - "Spent This Month":**
```
Rollup from: Related Transactions
Property: Amount
Filter: formatDate(prop("Date"), "MMMM YYYY") == formatDate(now(), "MMMM YYYY") and prop("Type") == "Expense"
Calculate: Sum
```

**In Accounts database - "Balance from Transactions":**
```
Rollup from: Related Transactions
Property: Amount
Calculate: Sum
```

### 4. Add Sample Data (20 minutes)

Copy the sample data from the main template into each database to test functionality.

### 5. Create Views (15 minutes)

**Monthly Expenses View (in Transactions):**
- Filter: Type = "Expense" AND Date = "This Month"
- Group by: Category
- Sort: Amount (Descending)

**Budget Overview (in Categories):**
- Filter: Type = "Expense"
- Sort: Remaining (Ascending)

**Investment Performance (in Investments):**
- Sort: Gain/Loss % (Descending)

### 6. Build Dashboard Page (20 minutes)

Create a new page and embed:
- Key metrics as callouts
- Database views for quick access
- Progress bars for goals
- Charts for visual representation

## 🔧 Formula Reference

### Essential Formulas

**Monthly Income (in summary):**
```
sum(filter(prop("Transactions"), prop("Type") == "Income" and formatDate(prop("Date"), "MMMM YYYY") == formatDate(now(), "MMMM YYYY")).map(current.prop("Amount")))
```

**Budget Status (in Categories):**
```
if(prop("Remaining") > prop("Budget") * 0.1, "✅ On Track", if(prop("Remaining") >= 0, "⚠️ Near Limit", "🚨 Over Budget"))
```

**Goal Progress Status (in Savings Goals):**
```
if(prop("Progress") >= 100, "✅ Completed", if(prop("Progress") >= 75, "🟢 Almost There", if(prop("Progress") >= 25, "🟡 In Progress", "🔴 Just Started")))
```

**Investment Gain/Loss % (in Investments):**
```
((prop("Current Value") - (prop("Shares/Units") * prop("Purchase Price"))) / (prop("Shares/Units") * prop("Purchase Price"))) * 100
```

## 📱 Mobile Optimization Tips

1. **Create simplified views** for mobile entry
2. **Use templates** for quick transaction entry
3. **Set up shortcuts** for common categories
4. **Use voice-to-text** for descriptions
5. **Create quick-action buttons** using Notion's button feature

## 🔄 Maintenance Schedule

**Daily (2 minutes):**
- Add new transactions

**Weekly (10 minutes):**
- Update account balances
- Review uncategorized transactions

**Monthly (30 minutes):**
- Complete budget review
- Update investment values
- Check goal progress
- Plan next month's budget

**Quarterly (1 hour):**
- Review and adjust categories
- Analyze spending trends
- Rebalance investment portfolio
- Update financial goals

## 🎯 Success Metrics

Track these KPIs monthly:
- Savings Rate (target: >20%)
- Budget Adherence (target: >80%)
- Emergency Fund Coverage (target: 3-6 months)
- Debt-to-Income Ratio (target: <30%)
- Investment Growth (track quarterly)

## 🆘 Common Issues & Fixes

**Issue: Rollups not updating**
- Solution: Check relation setup and filter syntax

**Issue: Formulas showing errors**
- Solution: Verify property names match exactly

**Issue: Views not filtering correctly**
- Solution: Check date formats and filter conditions

**Issue: Mobile performance slow**
- Solution: Reduce database size, use simpler views

---

*Setup Time: ~2 hours total*
*Difficulty: Intermediate*
*Maintenance: 15 minutes/week*
